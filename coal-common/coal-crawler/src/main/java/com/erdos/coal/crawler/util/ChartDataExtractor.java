package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import com.microsoft.playwright.options.WaitUntilState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 专门用于提取煤易宝网站图表数据的工具类
 * 支持神华外购、CCI指数、CCTD指数三种数据类型的提取
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public class ChartDataExtractor {

    private static final Logger logger = LoggerFactory.getLogger(ChartDataExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");

    /**
     * 数据去重辅助类
     */
    private static class DataDeduplicator {
        private final Set<String> processedRawData = new HashSet<>();
        private final Map<String, Map<String, String>> uniqueData = new LinkedHashMap<>();
        private int processedCount = 0;

        public boolean addData(String date, String calorific, String price, String rawData) {
            processedCount++;

            // 检查原始数据是否已处理
            if (processedRawData.contains(rawData)) {
                return false;
            }

            // 检查是否为有效数据
            if (!isValidData(calorific, price)) {
                return false;
            }

            Map<String, String> dateData = uniqueData.computeIfAbsent(date, k -> new LinkedHashMap<>());

            // 检查是否已有相同的数据
            if (dateData.containsKey(calorific) && dateData.get(calorific).equals(price)) {
                return false;
            }

            // 添加新数据
            dateData.put(calorific, price);
            processedRawData.add(rawData);
            logger.debug("添加新数据: {} - {}={}", date, calorific, price);
            return true;
        }

        private boolean isValidData(String calorific, String price) {
            try {
                // 提取价格数字
                String priceNum = price.replaceAll("[^0-9]", "");
                if (priceNum.isEmpty()) return false;

                int priceValue = Integer.parseInt(priceNum);

                // 价格范围验证（200-1000元）
                if (priceValue < 200 || priceValue > 1000) {
                    return false;
                }

                // 热值验证
                return calorific.contains("5500kCal") || calorific.contains("5000kCal") ||
                       calorific.contains("4500kCal") || calorific.contains("外购");

            } catch (Exception e) {
                return false;
            }
        }

        public Map<String, Map<String, String>> getUniqueData() {
            return uniqueData;
        }

        public int getProcessedCount() {
            return processedCount;
        }
    }

    /**
     * 提取煤易宝数据的主方法
     *
     * @param indexType 指数类型（SHENHUA/CCI/CCTD）
     * @return 提取到的数据，格式：Map<日期, Map<热值, 价格>>
     */
    public static Map<String, Map<String, String>> extractMeiyibaoData(IndexType indexType) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 可视化模式便于调试
                            .setTimeout(120000));

            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setViewportSize(1920, 1080)
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"));

            Page page = context.newPage();
            page.setDefaultTimeout(120000);

            try {
                logger.info("开始提取{}数据...", indexType.getName());

                // 访问煤易宝网站
                page.navigate(BASE_URL, new Page.NavigateOptions().setTimeout(120000));
                logger.info("页面导航成功，等待内容加载...");
                page.waitForTimeout(5000);

                // 检查页面是否加载成功
                String title = page.title();
                logger.info("页面标题: {}", title);

                // 点击对应的标签页
                clickIndexTab(page, indexType);
                page.waitForTimeout(3000);

                // 从Canvas折线图提取数据
                result = extractDataFromCanvas(page);

                // 如果Canvas提取失败，尝试从页面文本提取
                if (result.isEmpty()) {
                    logger.info("Canvas提取失败，尝试从页面文本提取...");
                    String pageText = page.textContent("body");
                    result = extractDataFromText(pageText, indexType);
                }

                logger.info("{}数据提取完成，共获取{}天的数据", indexType.getName(), result.size());

            } catch (Exception e) {
                logger.error("页面操作失败: {}", e.getMessage(), e);
            }

            page.close();
            context.close();
            browser.close();

        } catch (Exception e) {
            logger.error("浏览器操作失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 兼容测试类的方法，返回CoalIndexDataDto列表格式
     */
    public static List<CoalIndexDataDto> extractChartData(IndexType indexType) {
        List<CoalIndexDataDto> result = new ArrayList<>();

        Map<String, Map<String, String>> rawData = extractMeiyibaoData(indexType);

        // 转换数据格式
        for (Map.Entry<String, Map<String, String>> dateEntry : rawData.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                String calorific = priceEntry.getKey();
                String price = priceEntry.getValue();

                try {
                    CoalIndexDataDto dto = new CoalIndexDataDto();

                    // 解析日期字符串为Date对象
                    Date dataDate = parseDate(date);
                    dto.setDataDate(dataDate);

                    // 解析热值
                    Integer calorificValue = parseCalorificValue(calorific);
                    dto.setCalorificValue(calorificValue);

                    // 解析价格
                    BigDecimal priceValue = new BigDecimal(price.replaceAll("[^0-9]", ""));
                    dto.setPrice(priceValue);

                    dto.setIndexType(indexType);
                    dto.setSourceUrl(BASE_URL);
                    result.add(dto);
                } catch (Exception e) {
                    logger.debug("转换数据格式失败: {}", e.getMessage());
                }
            }
        }

        return result;
    }

    /**
     * 点击指定的指数标签页
     */
    private static void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabText = getTabText(indexType);
            logger.info("尝试点击{}标签页...", tabText);

            // 尝试多种选择器策略
            String[] selectors = {
                "text=" + tabText,
                "[data-tab='" + indexType.getCode().toLowerCase() + "']",
                "#tab-" + getTabId(indexType),
                ".tab-item:has-text('" + tabText + "')",
                "a:has-text('" + tabText + "')",
                "button:has-text('" + tabText + "')"
            };

            boolean clicked = false;
            for (String selector : selectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        page.click(selector);
                        page.waitForTimeout(3000);
                        logger.info("成功点击{}标签页", tabText);
                        clicked = true;
                        break;
                    }
                } catch (Exception e) {
                    logger.debug("选择器{}失败: {}", selector, e.getMessage());
                }
            }

            if (!clicked) {
                logger.warn("所有选择器都失败，使用默认标签页");
            }

        } catch (Exception e) {
            logger.warn("点击{}标签页失败: {}", indexType.getName(), e.getMessage());
        }
    }

    /**
     * 获取标签页文本
     */
    private static String getTabText(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "神华外购指数";
            case CCI:
                return "CCI指数";
            case CCTD:
                return "CCTD指数";
            default:
                return "神华外购指数";
        }
    }

    /**
     * 获取标签页ID
     */
    private static String getTabId(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "1";
            case CCI:
                return "2";
            case CCTD:
                return "3";
            default:
                return "3";
        }
    }

    /**
     * 从Canvas折线图提取数据
     */
    private static Map<String, Map<String, String>> extractDataFromCanvas(Page page) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        try {
            logger.info("开始从Canvas折线图提取数据...");

            // 查找coal-char类的元素
            Locator coalCharElement = page.locator(".coal-char");
            if (coalCharElement.count() == 0) {
                logger.warn("未找到class='coal-char'的元素");
                return result;
            }

            logger.info("找到coal-char元素，数量: {}", coalCharElement.count());

            // 查找canvas元素
            Locator canvasElement = coalCharElement.locator("canvas");
            if (canvasElement.count() == 0) {
                logger.warn("在coal-char元素中未找到canvas");
                return result;
            }

            logger.info("找到canvas元素，数量: {}", canvasElement.count());

            // 获取canvas的边界框
            BoundingBox canvasBounds = canvasElement.first().boundingBox();
            if (canvasBounds == null) {
                logger.warn("无法获取canvas的边界框");
                return result;
            }

            logger.info("Canvas边界框: x={}, y={}, width={}, height={}",
                       canvasBounds.x, canvasBounds.y, canvasBounds.width, canvasBounds.height);

            // 等待图表加载完成
            page.waitForTimeout(3000);

            // 在canvas上模拟鼠标移动，尝试触发tooltip
            result = extractDataByMouseInteraction(page, canvasElement.first(), canvasBounds);

        } catch (Exception e) {
            logger.error("从Canvas提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 通过鼠标交互提取数据（使用去重辅助类）
     */
    private static Map<String, Map<String, String>> extractDataByMouseInteraction(
            Page page, Locator canvas, BoundingBox bounds) {
        DataDeduplicator deduplicator = new DataDeduplicator();

        try {
            logger.info("开始鼠标交互提取数据...");

            // 计算折线图区域（通常在canvas的中间部分）
            double startX = bounds.x + bounds.width * 0.1; // 左边距10%
            double endX = bounds.x + bounds.width * 0.9;   // 右边距10%

            // 多层Y坐标采样，以捕获不同折线的数据
            double[] yPositions = {
                bounds.y + bounds.height * 0.4, // 上层30%
                bounds.y + bounds.height * 0.5, // 中层50%
                bounds.y + bounds.height * 0.6  // 下层70%
            };

            // 在X轴上分多个点进行采样（30个采样点）
            int samplePoints = 30;
            double stepX = (endX - startX) / samplePoints;

            for (int i = 0; i <= samplePoints; i++) {
                double currentX = startX + i * stepX;

                // 在每个Y位置进行采样
                for (double currentY : yPositions) {
                    try {
                        // 移动鼠标到当前位置
                        page.mouse().move(currentX, currentY);
                        page.waitForTimeout(300); // 等待tooltip显示

                        // 点击当前位置
                        page.mouse().click(currentX, currentY);
                        page.waitForTimeout(500); // 等待数据更新

                        // 尝试提取tooltip或弹出的数据
                        String tooltipData = extractTooltipData(page);
                        if (!tooltipData.isEmpty()) {
                            logger.debug("在位置({}, {})提取到数据: {}", currentX, currentY, tooltipData);
                            parseTooltipData(tooltipData, deduplicator);
                        }

                        // 尝试从页面文本中提取当前显示的数据
                        String currentPageText = page.textContent("body");
                        extractCurrentDisplayData(currentPageText, deduplicator);

                    } catch (Exception e) {
                        logger.debug("在位置({}, {})提取数据失败: {}", currentX, currentY, e.getMessage());
                    }
                }
            }

            logger.info("鼠标交互完成，处理了{}条原始数据，去重后提取到{}天的数据",
                       deduplicator.getProcessedCount(), deduplicator.getUniqueData().size());

        } catch (Exception e) {
            logger.error("鼠标交互提取数据失败: {}", e.getMessage(), e);
        }

        return deduplicator.getUniqueData();
    }

    /**
     * 提取tooltip数据
     */
    private static String extractTooltipData(Page page) {
        String[] tooltipSelectors = {
            ".tooltip",
            ".chart-tooltip",
            ".echarts-tooltip",
            "[class*='tooltip']",
            "[class*='tip']",
            ".popup",
            ".overlay"
        };

        for (String selector : tooltipSelectors) {
            try {
                Locator tooltipElement = page.locator(selector);
                if (tooltipElement.count() > 0 && tooltipElement.first().isVisible()) {
                    String text = tooltipElement.first().textContent();
                    if (text != null && !text.trim().isEmpty()) {
                        return text.trim();
                    }
                }
            } catch (Exception e) {
                logger.debug("提取tooltip失败，选择器: {}, 错误: {}", selector, e.getMessage());
            }
        }

        return "";
    }

    /**
     * 解析tooltip数据
     */
    private static void parseTooltipData(String tooltipData, DataDeduplicator deduplicator) {
        try {
            logger.debug("开始解析tooltip数据: {}", tooltipData);

            // 提取日期
            Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(tooltipData);

            if (dateMatcher.find()) {
                String date = dateMatcher.group(1);
                logger.debug("提取到日期: {}", date);

                // 移除日期部分，处理剩余的价格数据
                String priceData = tooltipData.substring(dateMatcher.end());

                // 神华外购数据格式：外购5500kCal=423元, 外购5000kCal=368元, 外购4500kCal=298元，外购神优2=474元
                Pattern shenhuaPattern = Pattern.compile("外购(\\d{4,5}kCal|神优2)=(\\d{3,})元");
                Matcher shenhuaMatcher = shenhuaPattern.matcher(priceData);

                while (shenhuaMatcher.find()) {
                    String calorific = "外购" + shenhuaMatcher.group(1);
                    String price = shenhuaMatcher.group(2) + "元";
                    deduplicator.addData(date, calorific, price, tooltipData);
                }

                // CCI/CCTD数据格式：5500kCal=648元, 5000kCal=581元, 4500kCal=517元
                Pattern standardPattern = Pattern.compile("(\\d{4,5}kCal)=(\\d{3,})元");
                Matcher standardMatcher = standardPattern.matcher(priceData);

                while (standardMatcher.find()) {
                    String calorific = standardMatcher.group(1);
                    String price = standardMatcher.group(2) + "元";
                    deduplicator.addData(date, calorific, price, tooltipData);
                }

                // 如果没有找到标准格式，尝试更宽松的匹配
                if (!priceData.isEmpty()) {
                    Pattern loosePricePattern = Pattern.compile("(\\d{3,})");
                    Matcher loosePriceMatcher = loosePricePattern.matcher(priceData);

                    // 按顺序匹配价格，通常是5500kCal, 5000kCal, 4500kCal的顺序
                    String[] calorificValues = {"5500kCal", "5000kCal", "4500kCal"};
                    int index = 0;

                    while (loosePriceMatcher.find() && index < calorificValues.length) {
                        try {
                            String price = loosePriceMatcher.group(1) + "元";
                            deduplicator.addData(date, calorificValues[index], price, tooltipData + "_" + index);
                            index++;
                        } catch (Exception e) {
                            logger.debug("宽松解析失败: {}", e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("解析tooltip数据失败: {}", e.getMessage());
        }
    }

    /**
     * 提取当前显示的数据
     */
    private static void extractCurrentDisplayData(String pageText, DataDeduplicator deduplicator) {
        try {
            // 查找当前页面中显示的价格信息
            String[] lines = pageText.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.contains("-") && (line.contains("kCal") || line.contains("元"))) {

                    // 神华外购格式
                    Pattern shenhuaPattern = Pattern.compile("(\\d{2}-\\d{2}).*?外购(\\d{4,5}kCal|神优2).*?(\\d{3,})元");
                    Matcher shenhuaMatcher = shenhuaPattern.matcher(line);

                    if (shenhuaMatcher.find()) {
                        String date = shenhuaMatcher.group(1);
                        String calorific = "外购" + shenhuaMatcher.group(2);
                        String price = shenhuaMatcher.group(3) + "元";
                        deduplicator.addData(date, calorific, price, line);
                        continue;
                    }

                    // 标准格式
                    Pattern standardPattern = Pattern.compile("(\\d{2}-\\d{2}).*?(\\d{4,5}kCal).*?(\\d{3,})元");
                    Matcher standardMatcher = standardPattern.matcher(line);

                    if (standardMatcher.find()) {
                        String date = standardMatcher.group(1);
                        String calorific = standardMatcher.group(2);
                        String price = standardMatcher.group(3) + "元";
                        deduplicator.addData(date, calorific, price, line);
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("提取当前显示数据失败: {}", e.getMessage());
        }
    }

    /**
     * 从页面文本提取数据（备用方法）
     */
    private static Map<String, Map<String, String>> extractDataFromText(String pageText, IndexType indexType) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        DataDeduplicator deduplicator = new DataDeduplicator();

        try {
            logger.info("开始从页面文本提取数据...");

            String[] lines = pageText.split("\n");
            for (String line : lines) {
                line = line.trim();

                // 查找包含日期和价格信息的行
                if (line.contains("-") && (line.contains("kCal") || line.contains("元"))) {

                    // 根据不同的指数类型使用不同的解析策略
                    switch (indexType) {
                        case SHENHUA:
                            parseShenhuaLine(line, deduplicator);
                            break;
                        case CCI:
                        case CCTD:
                            parseStandardLine(line, deduplicator);
                            break;
                    }
                }
            }

            result = deduplicator.getUniqueData();
            logger.info("文本提取完成，找到 {} 天的数据", result.size());

        } catch (Exception e) {
            logger.error("从文本提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 解析神华外购格式的行
     */
    private static void parseShenhuaLine(String line, DataDeduplicator deduplicator) {
        // 神华外购格式：07-15: 外购5500kCal=423元, 外购5000kCal=368元, 外购4500kCal=298元，外购神优2=474元
        Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
        Matcher dateMatcher = datePattern.matcher(line);

        if (dateMatcher.find()) {
            String date = dateMatcher.group(1);

            Pattern pricePattern = Pattern.compile("外购(\\d{4,5}kCal|神优2)=(\\d{3,})元");
            Matcher priceMatcher = pricePattern.matcher(line);

            while (priceMatcher.find()) {
                String calorific = "外购" + priceMatcher.group(1);
                String price = priceMatcher.group(2) + "元";
                deduplicator.addData(date, calorific, price, line);
            }
        }
    }

    /**
     * 解析标准格式的行
     */
    private static void parseStandardLine(String line, DataDeduplicator deduplicator) {
        // 标准格式：07-24: 5500kCal=648元, 5000kCal=581元, 4500kCal=517元
        Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
        Matcher dateMatcher = datePattern.matcher(line);

        if (dateMatcher.find()) {
            String date = dateMatcher.group(1);

            Pattern pricePattern = Pattern.compile("(\\d{4,5}kCal)=(\\d{3,})元");
            Matcher priceMatcher = pricePattern.matcher(line);

            while (priceMatcher.find()) {
                String calorific = priceMatcher.group(1);
                String price = priceMatcher.group(2) + "元";
                deduplicator.addData(date, calorific, price, line);
            }
        }
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        System.out.println("=== 煤易宝网站数据提取测试 ===");

        try {
            // 测试神华外购数据提取
            System.out.println("\n1. 测试神华外购数据提取:");
            Map<String, Map<String, String>> shenhuaData = extractMeiyibaoData(IndexType.SHENHUA);
            printData(shenhuaData, "神华外购");

            // 测试CCI指数数据提取
            System.out.println("\n2. 测试CCI指数数据提取:");
            Map<String, Map<String, String>> cciData = extractMeiyibaoData(IndexType.CCI);
            printData(cciData, "CCI指数");

            // 测试CCTD指数数据提取
            System.out.println("\n3. 测试CCTD指数数据提取:");
            Map<String, Map<String, String>> cctdData = extractMeiyibaoData(IndexType.CCTD);
            printData(cctdData, "CCTD指数");

        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 打印数据结果
     */
    private static void printData(Map<String, Map<String, String>> data, String indexName) {
        if (data.isEmpty()) {
            System.out.println(indexName + ": 未提取到数据");
            return;
        }

        System.out.println(indexName + " 数据:");
        for (Map.Entry<String, Map<String, String>> dateEntry : data.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            StringBuilder sb = new StringBuilder();
            sb.append(date).append(": ");

            boolean first = true;
            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                if (!first) sb.append(", ");
                sb.append(priceEntry.getKey()).append("=").append(priceEntry.getValue());
                first = false;
            }

            System.out.println(sb.toString());
        }
        System.out.println("共 " + data.size() + " 天的数据");
    }

    /**
     * 解析日期字符串为Date对象
     */
    private static Date parseDate(String dateStr) {
        try {
            // 假设当前年份，格式如：07-15
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);

            String fullDateStr = currentYear + "-" + dateStr;
            SimpleDateFormat fullFormat = new SimpleDateFormat("yyyy-MM-dd");
            return fullFormat.parse(fullDateStr);
        } catch (Exception e) {
            logger.debug("解析日期失败: {}", dateStr);
            return new Date(); // 返回当前日期作为默认值
        }
    }

    /**
     * 解析热值字符串为Integer
     */
    private static Integer parseCalorificValue(String calorificStr) {
        try {
            if (calorificStr.contains("5500")) {
                return 5500;
            } else if (calorificStr.contains("5000")) {
                return 5000;
            } else if (calorificStr.contains("4500")) {
                return 4500;
            } else if (calorificStr.contains("神优2")) {
                return 5800; // 神优2通常对应5800大卡
            }
            return null;
        } catch (Exception e) {
            logger.debug("解析热值失败: {}", calorificStr);
            return null;
        }
    }
}
