// package com.erdos.coal.crawler.test;
//
// import com.erdos.coal.crawler.enums.IndexType;
// import com.erdos.coal.crawler.util.ChartDataExtractor;
//
// import java.util.Map;
//
// /**
//  * ChartDataExtractor测试类
//  *
//  * <AUTHOR>
//  * @date 2025-08-04
//  */
// public class ChartDataExtractorTest {
//
//     public static void main(String[] args) {
//         System.out.println("=== 煤易宝网站数据提取测试 ===");
//         System.out.println("开始测试ChartDataExtractor类...");
//
//         try {
//             // 测试神华外购数据提取
//             testShenhuaExtraction();
//
//             // 测试CCI指数数据提取
//             testCCIExtraction();
//
//             // 测试CCTD指数数据提取
//             testCCTDExtraction();
//
//         } catch (Exception e) {
//             System.err.println("测试过程中发生错误: " + e.getMessage());
//             e.printStackTrace();
//         }
//
//         System.out.println("\n测试完成！");
//     }
//
//     /**
//      * 测试神华外购数据提取
//      */
//     private static void testShenhuaExtraction() {
//         System.out.println("\n1. 测试神华外购数据提取...");
//
//         try {
//             Map<String, Map<String, String>> shenhuaData = ChartDataExtractor.extractMeiyibaoData(IndexType.SHENHUA);
//
//             System.out.println("神华外购数据提取结果:");
//             System.out.println("- 提取到 " + shenhuaData.size() + " 天的数据");
//
//             if (!shenhuaData.isEmpty()) {
//                 System.out.println("- 数据样例:");
//                 int count = 0;
//                 for (Map.Entry<String, Map<String, String>> entry : shenhuaData.entrySet()) {
//                     if (count >= 3) break; // 只显示前3天的数据
//
//                     String date = entry.getKey();
//                     Map<String, String> prices = entry.getValue();
//
//                     System.out.print("  " + date + ": ");
//                     String[] priceList = new String[prices.size()];
//                     int i = 0;
//                     for (Map.Entry<String, String> priceEntry : prices.entrySet()) {
//                         priceList[i++] = priceEntry.getKey() + "=" + priceEntry.getValue();
//                     }
//                     System.out.println(String.join(", ", priceList));
//                     count++;
//                 }
//             } else {
//                 System.out.println("⚠ 未提取到神华外购数据");
//             }
//
//         } catch (Exception e) {
//             System.err.println("神华外购数据提取失败: " + e.getMessage());
//         }
//     }
//
//     /**
//      * 测试CCI指数数据提取
//      */
//     private static void testCCIExtraction() {
//         System.out.println("\n2. 测试CCI指数数据提取...");
//
//         try {
//             Map<String, Map<String, String>> cciData = ChartDataExtractor.extractMeiyibaoData(IndexType.CCI);
//
//             System.out.println("CCI指数数据提取结果:");
//             System.out.println("- 提取到 " + cciData.size() + " 天的数据");
//
//             if (!cciData.isEmpty()) {
//                 System.out.println("- 数据样例:");
//                 int count = 0;
//                 for (Map.Entry<String, Map<String, String>> entry : cciData.entrySet()) {
//                     if (count >= 3) break; // 只显示前3天的数据
//
//                     String date = entry.getKey();
//                     Map<String, String> prices = entry.getValue();
//
//                     System.out.print("  " + date + ": ");
//                     String[] priceList = new String[prices.size()];
//                     int i = 0;
//                     for (Map.Entry<String, String> priceEntry : prices.entrySet()) {
//                         priceList[i++] = priceEntry.getKey() + "=" + priceEntry.getValue();
//                     }
//                     System.out.println(String.join(", ", priceList));
//                     count++;
//                 }
//             } else {
//                 System.out.println("⚠ 未提取到CCI指数数据");
//             }
//
//         } catch (Exception e) {
//             System.err.println("CCI指数数据提取失败: " + e.getMessage());
//         }
//     }
//
//     /**
//      * 测试CCTD指数数据提取
//      */
//     private static void testCCTDExtraction() {
//         System.out.println("\n3. 测试CCTD指数数据提取...");
//
//         try {
//             Map<String, Map<String, String>> cctdData = ChartDataExtractor.extractMeiyibaoData(IndexType.CCTD);
//
//             System.out.println("CCTD指数数据提取结果:");
//             System.out.println("- 提取到 " + cctdData.size() + " 天的数据");
//
//             if (!cctdData.isEmpty()) {
//                 System.out.println("- 数据样例:");
//                 int count = 0;
//                 for (Map.Entry<String, Map<String, String>> entry : cctdData.entrySet()) {
//                     if (count >= 3) break; // 只显示前3天的数据
//
//                     String date = entry.getKey();
//                     Map<String, String> prices = entry.getValue();
//
//                     System.out.print("  " + date + ": ");
//                     String[] priceList = new String[prices.size()];
//                     int i = 0;
//                     for (Map.Entry<String, String> priceEntry : prices.entrySet()) {
//                         priceList[i++] = priceEntry.getKey() + "=" + priceEntry.getValue();
//                     }
//                     System.out.println(String.join(", ", priceList));
//                     count++;
//                 }
//             } else {
//                 System.out.println("⚠ 未提取到CCTD指数数据");
//             }
//
//         } catch (Exception e) {
//             System.err.println("CCTD指数数据提取失败: " + e.getMessage());
//         }
//     }
// }
